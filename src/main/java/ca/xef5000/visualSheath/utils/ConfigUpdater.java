package ca.xef5000.visualSheath.utils;

import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.plugin.java.JavaPlugin;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Utility class to update config files while preserving user settings
 */
public class ConfigUpdater {

    /**
     * Update a config file with new values from the default config
     * @param plugin The plugin instance
     * @param configName The name of the config file (e.g., "config.yml")
     * @return True if the config was updated, false otherwise
     */
    public static boolean updateConfig(JavaPlugin plugin, String configName) {
        File configFile = new File(plugin.getDataFolder(), configName);
        
        // If config doesn't exist, just save the default
        if (!configFile.exists()) {
            plugin.saveResource(configName, false);
            return true;
        }
        
        // Load the current config
        FileConfiguration currentConfig = YamlConfiguration.loadConfiguration(configFile);
        
        // Load the default config from the jar
        InputStream defaultConfigStream = plugin.getResource(configName);
        if (defaultConfigStream == null) {
            return false;
        }
        
        // Convert the default config stream to a reader
        Reader defaultConfigReader = new InputStreamReader(defaultConfigStream, StandardCharsets.UTF_8);
        
        // Load the default config
        FileConfiguration defaultConfig = YamlConfiguration.loadConfiguration(defaultConfigReader);
        
        // Check if the config needs updating
        boolean needsUpdate = false;
        for (String key : defaultConfig.getKeys(true)) {
            if (!currentConfig.contains(key)) {
                needsUpdate = true;
                break;
            }
        }
        
        // If no update needed, return
        if (!needsUpdate) {
            return false;
        }
        
        // Create a backup of the current config
        try {
            File backupFile = new File(plugin.getDataFolder(), configName + ".backup");
            copyFile(configFile, backupFile);
        } catch (IOException e) {
            plugin.getLogger().warning("Failed to create backup of config file: " + e.getMessage());
        }
        
        // Merge the configs
        Map<String, Object> mergedValues = new HashMap<>();
        mergeConfigs(defaultConfig, currentConfig, "", mergedValues);
        
        // Create a new config with the merged values
        FileConfiguration newConfig = new YamlConfiguration();
        for (Map.Entry<String, Object> entry : mergedValues.entrySet()) {
            newConfig.set(entry.getKey(), entry.getValue());
        }
        
        // Save the new config
        try {
            newConfig.save(configFile);
            plugin.getLogger().info("Updated " + configName + " with new settings.");
            return true;
        } catch (IOException e) {
            plugin.getLogger().warning("Failed to save updated config: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Merge two configs, preserving user values while adding new default values
     * @param defaultConfig The default config
     * @param currentConfig The current config
     * @param path The current path in the config
     * @param mergedValues Map to store merged values
     */
    private static void mergeConfigs(FileConfiguration defaultConfig, FileConfiguration currentConfig, 
                                    String path, Map<String, Object> mergedValues) {
        // Get all keys at the current path
        Set<String> keys;
        if (path.isEmpty()) {
            keys = defaultConfig.getKeys(false);
        } else {
            keys = defaultConfig.getConfigurationSection(path).getKeys(false);
        }
        
        // Process each key
        for (String key : keys) {
            String fullPath = path.isEmpty() ? key : path + "." + key;
            
            // If the key is a section, recurse
            if (defaultConfig.isConfigurationSection(fullPath)) {
                mergeConfigs(defaultConfig, currentConfig, fullPath, mergedValues);
            } else {
                // If the current config has this key, use its value, otherwise use the default
                if (currentConfig.contains(fullPath)) {
                    mergedValues.put(fullPath, currentConfig.get(fullPath));
                } else {
                    mergedValues.put(fullPath, defaultConfig.get(fullPath));
                }
            }
        }
        
        // Also include any sections/keys in the current config that aren't in the default
        if (!path.isEmpty() && currentConfig.contains(path) && currentConfig.isConfigurationSection(path)) {
            Set<String> currentKeys = currentConfig.getConfigurationSection(path).getKeys(false);
            for (String key : currentKeys) {
                String fullPath = path + "." + key;
                if (!defaultConfig.contains(fullPath)) {
                    mergedValues.put(fullPath, currentConfig.get(fullPath));
                }
            }
        } else if (path.isEmpty()) {
            Set<String> currentKeys = currentConfig.getKeys(false);
            for (String key : currentKeys) {
                if (!defaultConfig.contains(key)) {
                    if (currentConfig.isConfigurationSection(key)) {
                        addAllValues(currentConfig, key, mergedValues);
                    } else {
                        mergedValues.put(key, currentConfig.get(key));
                    }
                }
            }
        }
    }
    
    /**
     * Add all values from a configuration section to the merged values map
     * @param config The config to read from
     * @param path The path to start at
     * @param mergedValues Map to store merged values
     */
    private static void addAllValues(FileConfiguration config, String path, Map<String, Object> mergedValues) {
        if (config.isConfigurationSection(path)) {
            Set<String> keys = config.getConfigurationSection(path).getKeys(false);
            for (String key : keys) {
                String fullPath = path + "." + key;
                if (config.isConfigurationSection(fullPath)) {
                    addAllValues(config, fullPath, mergedValues);
                } else {
                    mergedValues.put(fullPath, config.get(fullPath));
                }
            }
        } else {
            mergedValues.put(path, config.get(path));
        }
    }
    
    /**
     * Copy a file
     * @param source The source file
     * @param dest The destination file
     * @throws IOException If an error occurs
     */
    private static void copyFile(File source, File dest) throws IOException {
        try (InputStream in = new FileInputStream(source);
             OutputStream out = new FileOutputStream(dest)) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = in.read(buffer)) > 0) {
                out.write(buffer, 0, length);
            }
        }
    }
}