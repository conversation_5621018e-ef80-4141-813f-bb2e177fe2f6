package ca.xef5000.visualSheath.managers;

import ca.xef5000.visualSheath.VisualSheath;
import ca.xef5000.visualSheath.utils.ItemsAdderIntegration;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.GameMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Manages the sheath functionality for players
 */
public class SheathManager {

    private final VisualSheath plugin;
    private final List<Material> sheathableVanillaItems;
    private final Map<String, Integer> sheathableCustomItems;
    private final List<String> allSheathableItems; // Stores the original order for priority

    /**
     * Constructor for SheathManager
     * @param plugin The VisualSheath plugin instance
     */
    public SheathManager(VisualSheath plugin) {
        this.plugin = plugin;
        this.sheathableVanillaItems = new ArrayList<>();
        this.sheathableCustomItems = new HashMap<>();
        this.allSheathableItems = new ArrayList<>();
        loadSheathableItems();
    }

    /**
     * Load sheathable items from config
     */
    private void loadSheathableItems() {
        List<String> configItems = plugin.getConfig().getStringList("sheathable");

        // Store the original list for priority ordering
        allSheathableItems.clear();
        allSheathableItems.addAll(configItems);

        // Clear existing items
        sheathableVanillaItems.clear();
        sheathableCustomItems.clear();

        // Process each item in the config
        int priority = 0;
        for (String itemName : configItems) {
            // Check if it's a custom item (contains a colon)
            if (itemName.contains(":")) {
                sheathableCustomItems.put(itemName, priority);
            } else {
                try {
                    Material material = Material.valueOf(itemName.toUpperCase());
                    sheathableVanillaItems.add(material);
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("Invalid material in config: " + itemName);
                }
            }
            priority++;
        }
    }

    /**
     * Check if a player has any sheathable items in their inventory
     * @param player The player to check
     * @return true if the player has any sheathable items
     */
    public boolean playerHasSheathableItems(Player player) {
        // Get all inventory contents except the offhand slot
        ItemStack[] contents = player.getInventory().getStorageContents();

        // Use a more efficient loop
        for (int i = 0; i < contents.length; i++) {
            ItemStack item = contents[i];
            if (item != null && isSheathable(item)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if an item is sheathable
     * @param item The item to check
     * @return true if the item is sheathable
     */
    public boolean isSheathable(ItemStack item) {
        if (item == null) {
            return false;
        }

        // Check vanilla items
        if (sheathableVanillaItems.contains(item.getType())) {
            return true;
        }

        // Check ItemsAdder items if enabled
        if (ItemsAdderIntegration.isEnabled() && ItemsAdderIntegration.isCustomItem(item)) {
            String namespacedID = ItemsAdderIntegration.getNamespacedID(item);
            return sheathableCustomItems.containsKey(namespacedID);
        }

        return false;
    }

    /**
     * Check if a material is sheathable
     * @param material The material to check
     * @return true if the material is sheathable
     */
    public boolean isSheathable(Material material) {
        return sheathableVanillaItems.contains(material);
    }

    /**
     * Reload sheathable items from config
     */
    public void reloadSheathableItems() {
        loadSheathableItems();
    }

    /**
     * Update a player's sheath based on their inventory
     * @param player The player to update
     */
    public void updatePlayerSheath(Player player) {
        // Skip if player is in a disabled world
        if (plugin.isWorldDisabled(player.getWorld().getName())) {
            return;
        }

        // Skip if player is in creative mode and creative mode is disabled
        if (player.getGameMode() == GameMode.CREATIVE && plugin.isDisabledInCreative()) {
            return;
        }

        // Check if player has any sheathable items
        boolean hasSheathableItems = playerHasSheathableItems(player);

        // Find the highest priority sheathable item in the player's inventory
        ItemStack sheathItem = null;

        // Get the item in the player's main hand
        ItemStack mainHandItem = player.getInventory().getItemInMainHand();
        boolean holdingSheathable = !mainHandItem.getType().equals(Material.AIR) && isSheathable(mainHandItem);

        // Only find a sheathable item if the player has one and isn't holding it
        if (hasSheathableItems && !holdingSheathable) {
            sheathItem = findSheathableItem(player);
        }

        // Check which visualization mode to use
        if (plugin.isEntityVisualizationMode()) {
            // Entity display mode
            if (sheathItem != null) {
                // Create a copy of the item for the display
                ItemStack displayCopy = sheathItem.clone();
                plugin.getEntityDisplayManager().updatePlayerDisplay(player, displayCopy);
            } else {
                // No sheathable item to display, remove any existing display
                plugin.getEntityDisplayManager().removePlayerDisplay(player.getUniqueId());
            }

        } else {
            // Offhand display mode (original behavior)

            // If player has no sheathable items, clear the offhand and return
            if (!hasSheathableItems) {
                // Only clear if there's a sheathable item in the offhand
                ItemStack offhandItem = player.getInventory().getItemInOffHand();
                if (offhandItem != null && offhandItem.getType() != Material.AIR &&
                    isSheathable(offhandItem)) {
                    player.getInventory().setItemInOffHand(new ItemStack(Material.AIR));
                }
                return;
            }

            // If the player is holding a sheathable item, clear the offhand
            if (holdingSheathable) {
                player.getInventory().setItemInOffHand(new ItemStack(Material.AIR));
                return;
            }

            // Set the offhand item
            if (sheathItem != null) {
                // Create a copy of the item for the sheath
                ItemStack sheathCopy = sheathItem.clone();
                player.getInventory().setItemInOffHand(sheathCopy);
            } else {
                // No sheathable item found, clear the offhand
                player.getInventory().setItemInOffHand(new ItemStack(Material.AIR));
            }

            // Make sure to remove any entity displays if they exist
            if (plugin.getEntityDisplayManager().hasPlayerDisplay(player.getUniqueId())) {
                plugin.getEntityDisplayManager().removePlayerDisplay(player.getUniqueId());
            }
        }
    }

    /**
     * Find the highest priority sheathable item in a player's inventory
     * @param player The player to check
     * @return The highest priority sheathable item, or null if none found
     */
    private ItemStack findSheathableItem(Player player) {
        // Get all inventory contents except the offhand slot
        ItemStack[] contents = player.getInventory().getStorageContents();

        // Check each item in the player's inventory and find the highest priority one
        ItemStack bestItem = null;
        int bestPriority = Integer.MAX_VALUE;

        for (ItemStack item : contents) {
            if (item == null || item.getType() == Material.AIR || isItemInMainHand(player, item)) {
                continue;
            }

            int priority = getSheathablePriority(item);
            if (priority >= 0 && priority < bestPriority) {
                bestItem = item;
                bestPriority = priority;
            }
        }

        return bestItem;
    }

    /**
     * Get the priority of a sheathable item (lower number = higher priority)
     * @param item The item to check
     * @return The priority, or -1 if not sheathable
     */
    private int getSheathablePriority(ItemStack item) {
        if (item == null) {
            return -1;
        }

        // Check vanilla items
        if (sheathableVanillaItems.contains(item.getType())) {
            return allSheathableItems.indexOf(item.getType().name());
        }

        // Check ItemsAdder items
        if (ItemsAdderIntegration.isEnabled() && ItemsAdderIntegration.isCustomItem(item)) {
            String namespacedID = ItemsAdderIntegration.getNamespacedID(item);
            if (sheathableCustomItems.containsKey(namespacedID)) {
                return sheathableCustomItems.get(namespacedID);
            }
        }

        return -1;
    }

    /**
     * Check if an item is in the player's main hand
     * @param player The player to check
     * @param item The item to check
     * @return true if the item is in the player's main hand
     */
    private boolean isItemInMainHand(Player player, ItemStack item) {
        ItemStack mainHandItem = player.getInventory().getItemInMainHand();
        return mainHandItem != null && mainHandItem.equals(item);
    }

    /**
     * Move any item in offhand to main inventory if player has sheathable items
     * @param player The player to check
     * @return true if an item was moved
     */
    public boolean moveOffhandToInventory(Player player) {
        // Skip if player is in a disabled world
        if (plugin.isWorldDisabled(player.getWorld().getName())) {
            return false;
        }

        // Skip if player is in creative mode and creative mode is disabled
        if (player.getGameMode() == GameMode.CREATIVE && plugin.isDisabledInCreative()) {
            return false;
        }

        // Check if player has any sheathable items
        boolean hasSheathableItems = playerHasSheathableItems(player);

        // If player has no sheathable items, don't modify offhand
        if (!hasSheathableItems) {
            return false;
        }

        // If using entity visualization mode, we don't need to move items from offhand
        // since we're not using the offhand slot for visualization
        if (plugin.isEntityVisualizationMode()) {
            return false;
        }

        // Get the item in offhand
        ItemStack offhandItem = player.getInventory().getItemInOffHand();

        // If offhand is empty or already has a sheathable item, no need to move
        if (offhandItem == null || offhandItem.getType() == Material.AIR ||
            isSheathable(offhandItem)) {
            return false;
        }

        // Try to add the offhand item to the main inventory
        HashMap<Integer, ItemStack> leftover = player.getInventory().addItem(offhandItem);

        // Clear the offhand
        player.getInventory().setItemInOffHand(new ItemStack(Material.AIR));

        // Drop any items that couldn't fit in the inventory
        if (!leftover.isEmpty()) {
            for (ItemStack item : leftover.values()) {
                player.getWorld().dropItemNaturally(player.getLocation(), item);
            }
        }

        return true;
    }
}
