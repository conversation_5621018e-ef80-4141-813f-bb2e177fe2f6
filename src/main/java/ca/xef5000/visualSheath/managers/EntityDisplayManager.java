package ca.xef5000.visualSheath.managers;

import ca.xef5000.visualSheath.VisualSheath;
import ca.xef5000.visualSheath.utils.ItemsAdderIntegration;
import org.bukkit.Location;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.*;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Transformation;
import org.joml.AxisAngle4f;
import org.joml.Quaternionf;
import org.joml.Vector3f;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Manages the display entities for the sheath visualization
 */
public class EntityDisplayManager {

    private final VisualSheath plugin;
    private final Map<UUID, List<DisplayInfo>> playerDisplays;
    private ModelManager modelManager;
    private float offsetX;
    private float offsetY;
    private float offsetZ;
    private float rotationX;
    private float rotationY;
    private float rotationZ;
    private float scale;
    private float crouchOffsetX;
    private float crouchOffsetY;
    private float crouchOffsetZ;
    private float crouchRotationX;
    private float crouchRotationY;
    private float crouchRotationZ;

    /**
     * Constructor for EntityDisplayManager
     * @param plugin The VisualSheath plugin instance
     */
    public EntityDisplayManager(VisualSheath plugin) {
        this.plugin = plugin;
        this.playerDisplays = new HashMap<>();
        loadConfigSettings();
    }

    /**
     * Set the model manager
     * @param modelManager The model manager
     */
    public void setModelManager(ModelManager modelManager) {
        this.modelManager = modelManager;
    }

    /**
     * Load configuration settings for the entity display
     */
    public void loadConfigSettings() {
        // Load offset values
        offsetX = (float) plugin.getConfig().getDouble("visualization.entity.offset.x", -0.15);
        offsetY = (float) plugin.getConfig().getDouble("visualization.entity.offset.y", 0.5);
        offsetZ = (float) plugin.getConfig().getDouble("visualization.entity.offset.z", -0.25);

        // Load rotation values (convert from degrees to radians)
        rotationX = (float) Math.toRadians(plugin.getConfig().getDouble("visualization.entity.rotation.x", 0));
        rotationY = (float) Math.toRadians(plugin.getConfig().getDouble("visualization.entity.rotation.y", 0));
        rotationZ = (float) Math.toRadians(plugin.getConfig().getDouble("visualization.entity.rotation.z", 45));

        // Load crouch offset values
        crouchOffsetX = (float) plugin.getConfig().getDouble("visualization.entity.crouch.offset.x", 0);
        crouchOffsetY = (float) plugin.getConfig().getDouble("visualization.entity.crouch.offset.y", -0.3);
        crouchOffsetZ = (float) plugin.getConfig().getDouble("visualization.entity.crouch.offset.z", 0.1);

        // Load crouch rotation values (convert from degrees to radians)
        crouchRotationX = (float) Math.toRadians(plugin.getConfig().getDouble("visualization.entity.crouch.rotation.x", 30));
        crouchRotationY = (float) Math.toRadians(plugin.getConfig().getDouble("visualization.entity.crouch.rotation.y", 0));
        crouchRotationZ = (float) Math.toRadians(plugin.getConfig().getDouble("visualization.entity.crouch.rotation.z", 0));

        // Load scale
        scale = (float) plugin.getConfig().getDouble("visualization.entity.scale", 0.5);
    }

    /**
     * Update or create display entities for a player
     * @param player The player to update
     * @param item The item to display, or null to remove the display
     */
    public void updatePlayerDisplay(Player player, ItemStack item) {
        UUID playerId = player.getUniqueId();

        // If the item is null, remove any existing displays and return
        if (item == null) {
            removePlayerDisplay(playerId);
            return;
        }

        // Check if there's a custom model for this item
        FileConfiguration modelConfig = null;
        List<ModelManager.EntityConfig> entityConfigs = new ArrayList<>();

        if (modelManager != null) {
            modelConfig = modelManager.getModelForItem(item);
            if (modelConfig != null) {
                entityConfigs = modelManager.getEntitiesFromModel(modelConfig);
            }
        }

        // If no custom model found or no entities in the model, use default settings
        if (entityConfigs.isEmpty()) {
            ModelManager.EntityConfig defaultConfig = new ModelManager.EntityConfig();
            defaultConfig.scale = scale;
            defaultConfig.offsetX = offsetX;
            defaultConfig.offsetY = offsetY;
            defaultConfig.offsetZ = offsetZ;
            defaultConfig.rotationX = (float) Math.toDegrees(rotationX);
            defaultConfig.rotationY = (float) Math.toDegrees(rotationY);
            defaultConfig.rotationZ = (float) Math.toDegrees(rotationZ);
            entityConfigs.add(defaultConfig);
        }

        // Create a new list for this player's displays
        List<DisplayInfo> displays = new ArrayList<>();

        List<DisplayInfo> currentDisplays = playerDisplays.get(playerId);
        boolean isNewDisplay = currentDisplays == null || currentDisplays.isEmpty();
        if (!isNewDisplay) {
            for (DisplayInfo displayInfo : currentDisplays) {
                ItemDisplay display = displayInfo.display;
                if (display == null || !display.isValid() || display.getVehicle() == null) {
                    removePlayerDisplay(playerId);
                    isNewDisplay = true;
                    break;
                }
            }
        }


        if (isNewDisplay) {
            // Create display entities for each entity config
            for (ModelManager.EntityConfig entityConfig : entityConfigs) {
                // Create a new display entity
                Location location = player.getLocation();
                location.setYaw(180f);
                location.setPitch(0f);
                ItemDisplay display = player.getWorld().spawn(location, ItemDisplay.class);

                // Set the item to display
                ItemStack displayItem = item;
                if (entityConfig.customItem != null && modelManager != null) {
                    ItemStack customItem = modelManager.getCustomItemFromModel(entityConfig.customItem);
                    if (customItem != null) {
                        displayItem = customItem;
                    }
                }
                display.setItemStack(displayItem);

                // Make it not persist when chunk unloads
                display.setPersistent(false);

                // Set other display properties
                display.setInvulnerable(true);

                display.setInterpolationDuration(1);
                display.setInterpolationDelay(-1);

                // Add the display as a passenger to the player
                player.addPassenger(display);

                // Update the display position with the entity config
                updatePlayerDisplayPosition(player, display, entityConfig);

                // Add to the list of displays
                displays.add(new DisplayInfo(display, entityConfig));

            }
            playerDisplays.put(playerId, displays);
        }

    }

    /**
     * Updates the position, rotation and scale of a display entity using default settings
     * @param player The player whose display is being updated
     * @param display The display entity to update
     */
    private void updatePlayerDisplayPosition(Player player, ItemDisplay display) {
        // Create a default entity config with the current settings
        ModelManager.EntityConfig config = new ModelManager.EntityConfig();
        config.scale = scale;
        config.offsetX = offsetX;
        config.offsetY = offsetY;
        config.offsetZ = offsetZ;
        config.rotationX = (float) Math.toDegrees(rotationX);
        config.rotationY = (float) Math.toDegrees(rotationY);
        config.rotationZ = (float) Math.toDegrees(rotationZ);

        // Update using the config
        updatePlayerDisplayPosition(player, display, config);
    }

    /**
     * Updates the position, rotation and scale of a display entity using a custom entity config
     * @param player The player whose display is being updated
     * @param display The display entity to update
     * @param config The entity configuration to apply
     */
    private void updatePlayerDisplayPosition(Player player, ItemDisplay display, ModelManager.EntityConfig config) {
        // Get player's yaw (horizontal rotation) and convert to radians
        float playerYaw = (float) Math.toRadians(player.getLocation().getYaw());
        
        // Check if player is sneaking
        boolean isSneaking = player.isSneaking();

        // Apply base offsets from config
        float finalOffsetX = config.offsetX;
        float finalOffsetY = config.offsetY;
        float finalOffsetZ = config.offsetZ;
        
        // Apply base rotations from config (in radians)
        float rotX = (float) Math.toRadians(config.rotationX);
        float rotY = (float) Math.toRadians(config.rotationY);
        float rotZ = (float) Math.toRadians(config.rotationZ);
        
        // Apply crouch adjustments if player is sneaking
        if (isSneaking) {
            // Apply global crouch offsets
            finalOffsetX += crouchOffsetX;
            finalOffsetY += crouchOffsetY;
            finalOffsetZ += crouchOffsetZ;
            
            // Apply global crouch rotations
            rotX += crouchRotationX;
            rotY += crouchRotationY;
            rotZ += crouchRotationZ;
            
            // Apply model-specific crouch adjustments if available
            if (config.crouchOffsetX != 0) finalOffsetX += config.crouchOffsetX;
            if (config.crouchOffsetY != 0) finalOffsetY += config.crouchOffsetY;
            if (config.crouchOffsetZ != 0) finalOffsetZ += config.crouchOffsetZ;
            
            if (config.crouchRotationX != 0) rotX += Math.toRadians(config.crouchRotationX);
            if (config.crouchRotationY != 0) rotY += Math.toRadians(config.crouchRotationY);
            if (config.crouchRotationZ != 0) rotZ += Math.toRadians(config.crouchRotationZ);
        }

        // Create rotation quaternions for the configured rotations
        Quaternionf rotationXQuat = new Quaternionf().rotateAxis(rotX, 1, 0, 0);
        Quaternionf rotationYQuat = new Quaternionf().rotateAxis(rotY, 0, 1, 0);
        Quaternionf rotationZQuat = new Quaternionf().rotateAxis(rotZ, 0, 0, 1);

        // Create player rotation quaternion (around Y axis only - ignoring pitch)
        Quaternionf playerRotation = new Quaternionf().rotateAxis(-playerYaw, 0, 1, 0);

        // Combine all rotations for the item orientation
        Quaternionf combinedRotation = playerRotation.mul(rotationXQuat).mul(rotationYQuat).mul(rotationZQuat);

        // Create the original offset vector
        Vector3f offsetVector = new Vector3f(finalOffsetX, finalOffsetY, finalOffsetZ);

        // Create a rotation quaternion for the offset vector (only using yaw)
        Quaternionf offsetRotation = new Quaternionf().rotateY(-playerYaw);

        // Apply the rotation to the offset vector
        Vector3f rotatedOffset = offsetRotation.transform(offsetVector);

        // Calculate the final scale, applying individual axis scaling
        float finalScaleX = config.scale * config.scaleX;
        float finalScaleY = config.scale * config.scaleY;
        float finalScaleZ = config.scale * config.scaleZ;

        // Set transformation (scale, rotation, translation)
        display.setTransformation(new Transformation(
                rotatedOffset,                      // rotated translation
                new AxisAngle4f(combinedRotation),  // rotation
                new Vector3f(finalScaleX, finalScaleY, finalScaleZ),  // scale with individual axis scaling
                new AxisAngle4f()                   // no right rotation
        ));
    }

    /**
     * Updates the position of a player's display entities
     * @param player The player whose displays should be updated
     */
    public void updatePlayerDisplayRotation(Player player) {
        UUID playerId = player.getUniqueId();
        List<DisplayInfo> displays = playerDisplays.get(playerId);

        if (displays != null && !displays.isEmpty()) {
            // Get the item from the first display
            for (DisplayInfo displayInfo : displays) {
                ItemDisplay display = displayInfo.display;
                if (display != null && display.isValid()) {
                    updatePlayerDisplayPosition(player, display, displayInfo.config);
                }
            }
        }
    }

    /**
     * Remove a player's display entities
     * @param playerId The UUID of the player
     */
    public void removePlayerDisplay(UUID playerId) {
        List<DisplayInfo> displays = playerDisplays.remove(playerId);
        if (displays != null) {
            for (DisplayInfo displayInfo : displays) {
                ItemDisplay display = displayInfo.display;
                if (display != null && display.isValid()) {
                    display.remove();
                }
            }
        }
    }

    /**
     * Remove all display entities
     */
    public void removeAllDisplays() {
        for (List<DisplayInfo> displays : playerDisplays.values()) {
            if (displays != null) {
                for (DisplayInfo displayInfo : displays) {
                    ItemDisplay display = displayInfo.display;
                    if (display != null && display.isValid()) {
                        display.remove();
                    }
                }
            }
        }
        playerDisplays.clear();
    }

    /**
     * Check for orphaned display entities and remove them
     */
    public void cleanupOrphanedDisplays() {
        new BukkitRunnable() {
            @Override
            public void run() {
                // Create a copy of the map to avoid concurrent modification
                Map<UUID, List<DisplayInfo>> displaysCopy = new HashMap<>(playerDisplays);

                for (Map.Entry<UUID, List<DisplayInfo>> entry : displaysCopy.entrySet()) {
                    UUID playerId = entry.getKey();
                    List<DisplayInfo> displays = entry.getValue();
                    boolean needsRemoval = false;

                    if (displays != null) {
                        for (DisplayInfo displayInfo : displays) {
                            ItemDisplay display = displayInfo.display;
                            // Check if the display is still valid and has a vehicle
                            if (display == null || !display.isValid() || display.getVehicle() == null) {
                                needsRemoval = true;
                                break;
                            }
                        }
                    } else {
                        needsRemoval = true;
                    }

                    if (needsRemoval) {
                        removePlayerDisplay(playerId);
                    }
                }
            }
        }.runTaskTimer(plugin, 100L, 100L); // Run every 5 seconds
    }

    /**
     * Handle player teleport to ensure display entities follow
     * @param player The player who teleported
     */
    public void handlePlayerTeleport(Player player) {
        UUID playerId = player.getUniqueId();
        List<DisplayInfo> displays = playerDisplays.get(playerId);

        if (displays != null && !displays.isEmpty()) {
            boolean needsUpdate = false;

            for (DisplayInfo displayInfo : displays) {
                ItemDisplay display = displayInfo.display;
                if (display != null && display.isValid()) {
                    // The display should automatically teleport with the player if it's a passenger
                    // But we'll check if it's still attached
                    if (display.getVehicle() == null || !display.getVehicle().equals(player)) {
                        // If not, we'll need to update all displays
                        needsUpdate = true;
                        break;
                    }
                } else {
                    needsUpdate = true;
                    break;
                }
            }

            if (needsUpdate) {
                // Get the item from the first valid display
                ItemStack item = null;
                for (DisplayInfo displayInfo : displays) {
                    ItemDisplay display = displayInfo.display;
                    if (display != null && display.isValid()) {
                        item = display.getItemStack();
                        break;
                    }
                }

                // If we found an item, update all displays
                if (item != null) {
                    updatePlayerDisplay(player, item);
                } else {
                    // Otherwise, remove all displays
                    removePlayerDisplay(playerId);
                }
            }
        }
    }

    /**
     * Check if a player has any display entities
     * @param playerId The UUID of the player
     * @return true if the player has at least one valid display entity
     */
    public boolean hasPlayerDisplay(UUID playerId) {
        List<DisplayInfo> displays = playerDisplays.get(playerId);
        if (displays == null || displays.isEmpty()) {
            return false;
        }

        // Check if at least one display is valid
        for (DisplayInfo displayInfo : displays) {
            ItemDisplay display = displayInfo.display;
            if (display != null && display.isValid()) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if an entity is a display entity managed by this manager
     * @param entity The entity to check
     * @return true if the entity is a managed display entity
     */
    public boolean isManagedDisplayEntity(Entity entity) {
        if (!(entity instanceof ItemDisplay)) {
            return false;
        }

        // Check all display lists
        for (List<DisplayInfo> displays : playerDisplays.values()) {
            if (displays != null && displays.contains(entity)) {
                return true;
            }
        }

        return false;
    }

    public class DisplayInfo {
        public ItemDisplay display;
        public ModelManager.EntityConfig config;

        public DisplayInfo(ItemDisplay display, ModelManager.EntityConfig config) {
            this.display = display;
            this.config = config;
        }
    }
}
