package ca.xef5000.visualSheath.listeners;

import ca.xef5000.visualSheath.VisualSheath;
import ca.xef5000.visualSheath.events.SheathableItemsCheckEvent;
import org.bukkit.GameMode;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.inventory.ItemStack;
import org.bukkit.Material;

/**
 * Handles the custom SheathableItemsCheckEvent
 */
public class SheathableItemsListener implements Listener {
    
    private final VisualSheath plugin;
    
    /**
     * Constructor for SheathableItemsListener
     * @param plugin The VisualSheath plugin instance
     */
    public SheathableItemsListener(VisualSheath plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Handle the SheathableItemsCheckEvent
     */
    @EventHandler
    public void onSheathableItemsCheck(SheathableItemsCheckEvent event) {
        Player player = event.getPlayer();
        
        // Skip if player is in creative mode and creative mode is disabled
        if (player.getGameMode() == GameMode.CREATIVE && plugin.isDisabledInCreative()) {
            return;
        }
        
        // If the player now has sheathable items but didn't before, move offhand items
        if (event.hasSheathableItems() && !event.hadSheathableItems()) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (player.isOnline()) {
                        if (!plugin.isEntityVisualizationMode()) {
                            plugin.getSheathManager().moveOffhandToInventory(player);
                            return;
                        }
                        plugin.getSheathManager().updatePlayerSheath(player);
                    }
                }
            }.runTaskLater(plugin, 1L);
        } 
        // If the player has sheathable items, ensure the sheath is updated
        else if (event.hasSheathableItems()) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (player.isOnline()) {
                        plugin.getSheathManager().updatePlayerSheath(player);
                    }
                }
            }.runTaskLater(plugin, 1L);
        }
        // If the player no longer has sheathable items, clear the offhand if it contains a sheathable item
        else if (!event.hasSheathableItems() && event.hadSheathableItems()) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (player.isOnline()) {
                        ItemStack offhandItem = player.getInventory().getItemInOffHand();
                        if (offhandItem != null && offhandItem.getType() != Material.AIR && 
                            plugin.getSheathManager().isSheathable(offhandItem.getType())) {
                            if (!plugin.isEntityVisualizationMode()) {
                                player.getInventory().setItemInOffHand(new ItemStack(Material.AIR));
                            }
                        }
                    }
                }
            }.runTaskLater(plugin, 1L);
        }
    }
}