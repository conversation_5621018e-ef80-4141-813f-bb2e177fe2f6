package ca.xef5000.visualSheath.listeners;

import ca.xef5000.visualSheath.VisualSheath;
import com.destroystokyo.paper.event.server.ServerTickStartEvent;
import org.bukkit.GameMode;
import org.bukkit.Material;
import org.bukkit.entity.Entity;
import org.bukkit.entity.ItemDisplay;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.event.inventory.InventoryType;
import org.bukkit.event.player.PlayerChangedWorldEvent;
import org.bukkit.event.player.PlayerDropItemEvent;
import org.bukkit.event.player.PlayerItemHeldEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.PlayerRespawnEvent;
import org.bukkit.event.player.PlayerSwapHandItemsEvent;
import org.bukkit.event.player.PlayerTeleportEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.event.entity.EntityPickupItemEvent;
import org.bukkit.event.player.PlayerToggleSneakEvent;

/**
 * Handles all player-related events for the sheath functionality
 */
public class PlayerListener implements Listener {

    private final VisualSheath plugin;

    /**
     * Constructor for PlayerListener
     * @param plugin The VisualSheath plugin instance
     */
    public PlayerListener(VisualSheath plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onServerTick(ServerTickStartEvent event) {
        if (!plugin.isEntityVisualizationMode()) {
            return;
        }
        // Update all player displays
        for (Player player : plugin.getServer().getOnlinePlayers()) {
            // Skip players in disabled worlds
            if (plugin.isWorldDisabled(player.getWorld().getName())) {
                continue;
            }

            // Skip players in creative mode if disabled in config
            if (player.getGameMode() == GameMode.CREATIVE && plugin.isDisabledInCreative()) {
                continue;
            }

            // Update the display position if player has a display
            if (plugin.getEntityDisplayManager().hasPlayerDisplay(player.getUniqueId())) {
                plugin.getEntityDisplayManager().updatePlayerDisplayRotation(player);
            }
        }
    }


    /**
     * Update sheath when player changes held item
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onItemHeldChange(PlayerItemHeldEvent event) {
        Player player = event.getPlayer();

        // Schedule a task to update the sheath after the item change
        new BukkitRunnable() {
            @Override
            public void run() {
                if (player.isOnline()) {
                    plugin.getSheathManager().updatePlayerSheath(player);
                }
            }
        }.runTaskLater(plugin, 1L);
    }

    /**
     * Handle player quit event to clean up tracking
     */
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();

        // Remove player from inventory tracking
        plugin.getInventoryTrackingManager().removePlayer(player.getUniqueId());

        // Remove any display entities for this player
        if (plugin.isEntityVisualizationMode()) {
            plugin.getEntityDisplayManager().removePlayerDisplay(player.getUniqueId());
        }
    }

    /**
     * Handle player teleport to ensure display entity follows
     */
    @EventHandler
    public void onPlayerTeleport(PlayerTeleportEvent event) {
        // Only handle if using entity visualization mode
        if (!plugin.isEntityVisualizationMode()) {
            return;
        }

        Player player = event.getPlayer();

        // Schedule a task to ensure the display entity is properly attached after teleport
        new BukkitRunnable() {
            @Override
            public void run() {
                if (player.isOnline()) {
                    plugin.getEntityDisplayManager().handlePlayerTeleport(player);
                }
            }
        }.runTaskLater(plugin, 2L);
    }

    /**
     * Prevent damage to display entities
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onEntityDamage(EntityDamageByEntityEvent event) {
        // Only handle if using entity visualization mode
        if (!plugin.isEntityVisualizationMode()) {
            return;
        }

        Entity entity = event.getEntity();

        // Check if the damaged entity is one of our display entities
        if (entity instanceof ItemDisplay && plugin.getEntityDisplayManager().isManagedDisplayEntity(entity)) {
            // Cancel the damage event
            event.setCancelled(true);
        }
    }

    /**
     * Update sheath when player joins
     */
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();

        // Check the player's inventory status
        plugin.getInventoryTrackingManager().checkPlayer(player);

        // Schedule a task to update the sheath after the player joins
        new BukkitRunnable() {
            @Override
            public void run() {
                if (player.isOnline()) {
                    plugin.getSheathManager().updatePlayerSheath(player);
                }
            }
        }.runTaskLater(plugin, 5L);
    }

    /**
     * Update sheath when player changes worlds
     */
    @EventHandler
    public void onWorldChange(PlayerChangedWorldEvent event) {
        Player player = event.getPlayer();

        // Schedule a task to update the sheath after the world change
        new BukkitRunnable() {
            @Override
            public void run() {
                if (player.isOnline()) {
                    plugin.getSheathManager().updatePlayerSheath(player);
                }
            }
        }.runTaskLater(plugin, 1L);
    }

    /**
     * Prevent swapping items between hands
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onSwapHandItems(PlayerSwapHandItemsEvent event) {
        if (plugin.isEntityVisualizationMode()) {
            return;
        }

        Player player = event.getPlayer();

        // Allow if in creative mode and creative mode is disabled in config
        if (player.getGameMode() == GameMode.CREATIVE && plugin.isDisabledInCreative()) {
            return;
        }

        // Allow if in disabled world
        if (plugin.isWorldDisabled(player.getWorld().getName())) {
            return;
        }

        // Allow if player has no sheathable items
        if (!plugin.getSheathManager().playerHasSheathableItems(player)) {
            return;
        }

        event.setCancelled(true);
    }

    /**
     * Prevent clicking on offhand slot to take items
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryClick(InventoryClickEvent event) {
        if (plugin.isEntityVisualizationMode()) {
            return;
        }
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();

        // Allow if in creative mode and creative mode is disabled in config
        if (player.getGameMode() == GameMode.CREATIVE && plugin.isDisabledInCreative()) {
            return;
        }

        // Allow if in disabled world
        if (plugin.isWorldDisabled(player.getWorld().getName())) {
            return;
        }

        // Allow if player has no sheathable items
        if (!plugin.getSheathManager().playerHasSheathableItems(player)) {
            return;
        }

        // Clicking on the offhand slot, but we have sheathable items in our inventory, need to cancel
        if (event.getSlotType() == InventoryType.SlotType.QUICKBAR && event.getSlot() == 40) {
            event.setCancelled(true);
            player.updateInventory();
        }

        // Check for shift-clicking items into offhand
        if (event.isShiftClick() && event.getClick() == ClickType.SHIFT_LEFT ||
            event.getClick() == ClickType.SHIFT_RIGHT) {

            // Schedule a task to update the sheath after the inventory change
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (player.isOnline()) {
                        plugin.getSheathManager().moveOffhandToInventory(player);
                        plugin.getSheathManager().updatePlayerSheath(player);
                    }
                }
            }.runTaskLater(plugin, 1L);
        }
    }

    /**
     * Prevent dragging items into offhand slot
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onInventoryDrag(InventoryDragEvent event) {
        if (plugin.isEntityVisualizationMode()) {
            return;
        }
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();

        // Allow if in creative mode and creative mode is disabled in config
        if (player.getGameMode() == GameMode.CREATIVE && plugin.isDisabledInCreative()) {
            return;
        }

        // Allow if in disabled world
        if (plugin.isWorldDisabled(player.getWorld().getName())) {
            return;
        }

        // Allow if player has no sheathable items
        if (!plugin.getSheathManager().playerHasSheathableItems(player)) {
            return;
        }

        // Check if the drag includes the offhand slot (slot 40)
        if (event.getInventorySlots().contains(40)) {
            event.setCancelled(true);

            // Update the player's inventory
            player.updateInventory();

            // Update the sheath
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (player.isOnline()) {
                        plugin.getSheathManager().updatePlayerSheath(player);
                    }
                }
            }.runTaskLater(plugin, 1L);
        }
    }

    /**
     * Prevent dropping items from offhand
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onItemDrop(PlayerDropItemEvent event) {
        if (plugin.isEntityVisualizationMode()) {
            return;
        }
        Player player = event.getPlayer();

        // Allow if in creative mode and creative mode is disabled in config
        if (player.getGameMode() == GameMode.CREATIVE && plugin.isDisabledInCreative()) {
            return;
        }

        // Allow if in disabled world
        if (plugin.isWorldDisabled(player.getWorld().getName())) {
            return;
        }

        // Schedule a task to update the sheath after the item drop
        new BukkitRunnable() {
            @Override
            public void run() {
                if (player.isOnline()) {
                    plugin.getSheathManager().updatePlayerSheath(player);
                }
            }
        }.runTaskLater(plugin, 1L);
    }

    /**
     * Handle player death - don't drop sheath items
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerDeath(PlayerDeathEvent event) {
        Player player = event.getEntity();

        // Allow if in disabled world
        if (plugin.isWorldDisabled(player.getWorld().getName())) {
            return;
        }

        // If using entity visualization mode, remove the display entity
        if (plugin.isEntityVisualizationMode()) {
            plugin.getEntityDisplayManager().removePlayerDisplay(player.getUniqueId());
        } else {
            // Using offhand mode - handle offhand item
            ItemStack offHandItem = player.getInventory().getItemInOffHand();

            // If there's a sheathable item in the offhand, remove it from drops
            if (offHandItem != null && offHandItem.getType() != Material.AIR &&
                plugin.getSheathManager().isSheathable(offHandItem.getType())) {

                // Remove the offhand item from drops
                event.getDrops().remove(offHandItem);

                // Clear the offhand slot
                player.getInventory().setItemInOffHand(new ItemStack(Material.AIR));
            }
        }
    }

    /**
     * Update sheath when player respawns
     */
    @EventHandler
    public void onPlayerRespawn(PlayerRespawnEvent event) {
        Player player = event.getPlayer();

        // Schedule a task to update the sheath after respawn
        new BukkitRunnable() {
            @Override
            public void run() {
                if (player.isOnline()) {
                    plugin.getSheathManager().updatePlayerSheath(player);
                }
            }
        }.runTaskLater(plugin, 5L);
    }

    /**
     * Handle when a player picks up an item
     */
    @EventHandler
    public void onItemPickup(EntityPickupItemEvent event) {
        if (!(event.getEntity() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getEntity();

        // Allow if in creative mode and creative mode is disabled in config
        if (player.getGameMode() == GameMode.CREATIVE && plugin.isDisabledInCreative()) {
            return;
        }

        // Allow if in disabled world
        if (plugin.isWorldDisabled(player.getWorld().getName())) {
            return;
        }

        ItemStack pickedItem = event.getItem().getItemStack();

        // If the picked up item is sheathable, schedule a task to update
        if (plugin.getSheathManager().isSheathable(pickedItem.getType())) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    if (player.isOnline()) {
                        if (!plugin.isEntityVisualizationMode()) {
                            plugin.getSheathManager().moveOffhandToInventory(player);
                            return;
                        }
                        plugin.getSheathManager().updatePlayerSheath(player);
                    }
                }
            }.runTaskLater(plugin, 1L);
        }
    }

    /**
     * Handle player sneaking to update display position
     */
    @EventHandler
    public void onPlayerToggleSneak(PlayerToggleSneakEvent event) {
        // Only handle if using entity visualization mode
        if (!plugin.isEntityVisualizationMode()) {
            return;
        }

        Player player = event.getPlayer();

        // Skip players in disabled worlds
        if (plugin.isWorldDisabled(player.getWorld().getName())) {
            return;
        }

        // Skip players in creative mode if disabled in config
        if (player.getGameMode() == GameMode.CREATIVE && plugin.isDisabledInCreative()) {
            return;
        }

        // Update the display position if player has a display
        if (plugin.getEntityDisplayManager().hasPlayerDisplay(player.getUniqueId())) {
            plugin.getEntityDisplayManager().updatePlayerDisplayRotation(player);
        }
    }
}
