package ca.xef5000.visualSheath.commands;

import ca.xef5000.visualSheath.VisualSheath;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ReloadCommand implements CommandExecutor, TabCompleter {

    private final VisualSheath plugin;

    public ReloadCommand(VisualSheath plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (args.length == 0) {
            sender.sendMessage(ChatColor.GOLD + "VisualSheath " + plugin.getDescription().getVersion());
            sender.sendMessage(ChatColor.YELLOW + "Use /vs reload to reload the configuration and models");
            return true;
        }

        if (args[0].equalsIgnoreCase("reload")) {
            if (!sender.hasPermission("visualsheath.reload")) {
                sender.sendMessage(ChatColor.RED + "You don't have permission to use this command.");
                return true;
            }

            plugin.reloadConfig();
            sender.sendMessage(ChatColor.GREEN + "VisualSheath configuration and models reloaded successfully!");
            return true;
        }

        sender.sendMessage(ChatColor.RED + "Unknown command. Use /vs reload to reload the configuration and models");
        return true;
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (args.length == 1) {
            if (sender.hasPermission("visualsheath.reload")) {
                return Arrays.asList("reload");
            }
        }
        return new ArrayList<>();
    }
}