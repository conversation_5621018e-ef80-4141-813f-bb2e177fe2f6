package ca.xef5000.visualSheath;

import ca.xef5000.visualSheath.commands.ReloadCommand;
import ca.xef5000.visualSheath.listeners.PlayerListener;
import ca.xef5000.visualSheath.listeners.SheathableItemsListener;
import ca.xef5000.visualSheath.managers.EntityDisplayManager;
import ca.xef5000.visualSheath.managers.InventoryTrackingManager;
import ca.xef5000.visualSheath.managers.ModelManager;
import ca.xef5000.visualSheath.managers.SheathManager;
import ca.xef5000.visualSheath.utils.ConfigUpdater;
import ca.xef5000.visualSheath.utils.ItemsAdderIntegration;
import org.bukkit.event.HandlerList;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.List;

public final class VisualSheath extends JavaPlugin {

    private SheathManager sheathManager;
    private InventoryTrackingManager inventoryTrackingManager;
    private EntityDisplayManager entityDisplayManager;
    private ModelManager modelManager;
    private List<String> disabledWorlds;
    private boolean disableInCreative;
    private String visualizationMode;

    @Override
    public void onEnable() {
        // Save default config if it doesn't exist
        saveDefaultConfig();

        // Update the config with any new values
        ConfigUpdater.updateConfig(this, "config.yml");

        // Initialize ItemsAdder integration
        ItemsAdderIntegration.initialize(this);

        // Load configuration settings
        loadConfigSettings();

        // Initialize managers
        modelManager = new ModelManager(this);
        modelManager.loadAllModels();

        sheathManager = new SheathManager(this);
        inventoryTrackingManager = new InventoryTrackingManager(this);
        entityDisplayManager = new EntityDisplayManager(this);

        // Connect managers
        entityDisplayManager.setModelManager(modelManager);

        // Register event listeners
        getServer().getPluginManager().registerEvents(new PlayerListener(this), this);
        getServer().getPluginManager().registerEvents(new SheathableItemsListener(this), this);

        // Register commands
        ReloadCommand reloadCommand = new ReloadCommand(this);
        getCommand("visualsheath").setExecutor(reloadCommand);
        getCommand("visualsheath").setTabCompleter(reloadCommand);

        // Start tracking player inventories
        inventoryTrackingManager.startTracking();

        // Start cleanup task for display entities
        entityDisplayManager.cleanupOrphanedDisplays();

        getLogger().info("VisualSheath has been enabled!");
    }

    /**
     * Load all configuration settings
     */
    public void loadConfigSettings() {
        // Load disabled worlds from config
        disabledWorlds = getConfig().getStringList("disabled-worlds");

        // Load creative mode setting
        disableInCreative = getConfig().getBoolean("disable-in-creative", true);

        // Load visualization mode
        visualizationMode = getConfig().getString("visualization.mode", "offhand");

        // Reload sheathable items if manager exists
        if (sheathManager != null) {
            sheathManager.reloadSheathableItems();
        }

        // Reload entity display settings if manager exists
        if (entityDisplayManager != null) {
            entityDisplayManager.loadConfigSettings();
        }

        // Reload models if manager exists
        if (modelManager != null) {
            modelManager.reloadModels();
        }
    }

    /**
     * Reload the plugin configuration
     */
    @Override
    public void reloadConfig() {
        super.reloadConfig();

        // Update the config with any new values
        ConfigUpdater.updateConfig(this, "config.yml");

        // Load configuration settings
        loadConfigSettings();

        // Force check all players with new settings
        if (inventoryTrackingManager != null) {
            inventoryTrackingManager.forceCheckAllPlayers();
        }
    }

    @Override
    public void onDisable() {
        // Stop tracking player inventories
        if (inventoryTrackingManager != null) {
            inventoryTrackingManager.stopTracking();
        }

        // Remove all display entities
        if (entityDisplayManager != null) {
            entityDisplayManager.removeAllDisplays();
        }

        // Unregister all event listeners
        HandlerList.unregisterAll(this);

        getLogger().info("VisualSheath has been disabled!");
    }

    /**
     * Get the sheath manager instance
     * @return SheathManager instance
     */
    public SheathManager getSheathManager() {
        return sheathManager;
    }

    /**
     * Get the inventory tracking manager instance
     * @return InventoryTrackingManager instance
     */
    public InventoryTrackingManager getInventoryTrackingManager() {
        return inventoryTrackingManager;
    }

    /**
     * Check if the plugin is disabled in a specific world
     * @param worldName Name of the world to check
     * @return true if the plugin is disabled in the world
     */
    public boolean isWorldDisabled(String worldName) {
        return disabledWorlds.contains(worldName);
    }

    /**
     * Check if the plugin should be disabled in creative mode
     * @return true if the plugin should be disabled in creative mode
     */
    public boolean isDisabledInCreative() {
        return disableInCreative;
    }

    /**
     * Get the entity display manager instance
     * @return EntityDisplayManager instance
     */
    public EntityDisplayManager getEntityDisplayManager() {
        return entityDisplayManager;
    }

    /**
     * Get the model manager
     * @return The model manager
     */
    public ModelManager getModelManager() {
        return modelManager;
    }

    /**
     * Check if the visualization mode is set to entity
     * @return true if the visualization mode is entity
     */
    public boolean isEntityVisualizationMode() {
        return "entity".equalsIgnoreCase(visualizationMode);
    }
}