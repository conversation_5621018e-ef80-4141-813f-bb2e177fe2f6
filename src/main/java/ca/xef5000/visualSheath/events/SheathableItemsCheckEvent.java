package ca.xef5000.visualSheath.events;

import org.bukkit.entity.Player;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

/**
 * Event fired when checking if a player has sheathable items
 */
public class SheathableItemsCheckEvent extends Event {
    private static final HandlerList handlers = new HandlerList();
    private final Player player;
    private final boolean hasSheathableItems;
    private final boolean hadSheathableItems;

    /**
     * Constructor for SheathableItemsCheckEvent
     * @param player The player being checked
     * @param hasSheathableItems Whether the player currently has sheathable items
     * @param hadSheathableItems Whether the player previously had sheathable items
     */
    public SheathableItemsCheckEvent(Player player, boolean hasSheathableItems, boolean hadSheathableItems) {
        this.player = player;
        this.hasSheathableItems = hasSheathableItems;
        this.hadSheathableItems = hadSheathableItems;
    }

    /**
     * Get the player being checked
     * @return The player
     */
    public Player getPlayer() {
        return player;
    }

    /**
     * Check if the player has sheathable items
     * @return true if the player has sheathable items
     */
    public boolean hasSheathableItems() {
        return hasSheathableItems;
    }

    /**
     * Check if the player previously had sheathable items
     * @return true if the player previously had sheathable items
     */
    public boolean hadSheathableItems() {
        return hadSheathableItems;
    }

    /**
     * Check if the player's sheathable items status has changed
     * @return true if the status has changed
     */
    public boolean hasStatusChanged() {
        return hasSheathableItems != hadSheathableItems;
    }

    @Override
    public HandlerList getHandlers() {
        return handlers;
    }

    public static HandlerList getHandlerList() {
        return handlers;
    }
}