# VisualSheath Model Configuration for Trident
# This is an example model configuration

# List of entities to display
entities:
  - # First entity (main trident)
    scale: 0.5  # Overall scale factor
    # Optional individual axis scaling
    scalex: 1.0
    scaley: 1.0
    scalez: 1.0
    # Offset from player position (in blocks)
    offset:
      x: -0.35
      y: -0.25
      z: 0.05
    # Rotation (in degrees)
    rotation:
      x: 0
      y: 0
      z: 45
    # Crouch-specific adjustments
    crouch:
      offset:
        x: 0
        y: -0.2
        z: 0.15
      rotation:
        x: 25
        y: 0
        z: 0
    # Optional: specify a different item to display
    # item: TRIDENT
    # For ItemsAdder items, use namespace:id format (e.g., itemsadder:ruby_trident)
    # item: itemsadder:ruby_trident

  - # Second entity (decorative element)
    scale: 0.3
    offset:
      x: -0.25
      y: -0.15
      z: 0.3
    rotation:
      x: 0
      y: 0
      z: 90
    # Crouch-specific adjustments
    crouch:
      offset:
        x: 0
        y: -0.2
        z: 0.1
    # Using a different item for this entity
    item: PRISMARINE_SHARD

  - # Third entity (decorative element)
    scale: 0.3
    offset:
      x: -0.10
      y: -0.04
      z: 0.3
    rotation:
      x: 0
      y: 180
    # Crouch-specific adjustments
    crouch:
      offset:
        y: -0.2
        z: 0.1
    item: PRISMARINE_SHARD
