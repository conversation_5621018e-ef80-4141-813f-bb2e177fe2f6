name: VisualSheath
version: '${version}'
main: ca.xef5000.visualSheath.VisualSheath
api-version: '1.20'
description: 'Automatically places copies of items in players'' offhand slots when not in use'
author: xef5000
softdepend: [ItemsAdder]

commands:
  visualsheath:
    description: VisualSheath commands
    aliases: [vs]
    usage: /vs reload
    permission: visualsheath.command

permissions:
  visualsheath.command:
    description: Allows access to VisualSheath commands
    default: op
  visualsheath.reload:
    description: Allows reloading the plugin configuration
    default: op
