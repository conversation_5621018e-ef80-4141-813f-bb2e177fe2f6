# VisualSheath
Add a visual sheath to your minecraft server!

## Config
```yaml
# VisualSheath Configuration

# List of items that can be put in the sheath
# Items higher in the list have priority when multiple sheathable items are in inventory
# For vanilla items, use the material name (e.g., DIAMOND_SWORD)
# For ItemsAdder items, use namespace:id format (e.g., itemsadder:ruby_sword, namespace:emerald_sword)
sheathable:
  - DIAMOND_SWORD
  - IRON_SWORD
  - GOLDEN_SWORD
  - STONE_SWORD
  - WOODEN_SWORD
  - NETHERITE_SWORD
  - TRIDENT
  - itemsadder:ruby_sword
  - itemsadder:emerald_sword
  - itemsadder:obsidian_sword

# List of worlds where the plugin is disabled
disabled-worlds:
  - world_nether
  - world_the_end

# Whether to disable the plugin for players in creative mode
disable-in-creative: true

# Visualization settings
visualization:
  # Mode can be either 'offhand' or 'entity'
  # offhand: Uses the player's offhand slot to display the sheathed item (original behavior)
  # entity: Uses an item_display entity on the player's back
  mode: offhand

  # Settings for entity display mode
  entity:
    # Offset from the player's position (in blocks)
    offset:
      x: -0.15
      y: 0.5
      z: -0.25
    # Rotation of the item (in degrees)
    rotation:
      x: 0
      y: 0
      z: 45
    # Scale of the item (1.0 = normal size)
    scale: 0.5
    # Adjustments when player is sneaking/crouching
    crouch:
      # Additional offset applied when crouching
      offset:
        x: 0
        y: -0.3
        z: 0.1
      # Additional rotation applied when crouching (in degrees)
      rotation:
        x: 30
        y: 0
        z: 0

# Custom model mappings
# Map items to specific model files in the models/ folder
# For vanilla items, use the material name (e.g., DIAMOND_SWORD)
# For ItemsAdder items, use namespace:id format (e.g., itemsadder:ruby_sword)
models:
  TRIDENT: Trident.yml
  # itemsadder:ruby_sword: RubySword.yml

# Performance settings
performance:
  # Number of players to check per tick
  batch-size: 20
  # How often to start a new check cycle (in ticks, 20 ticks = 1 second)
  check-interval: 40

```

## Models

Models are located in the `models` folder inside the plugin's folder.
Example model:
```yaml
# VisualSheath Model Configuration for Trident
# This is an example model configuration

# List of entities to display
entities:
  - # First entity (main trident)
    scale: 0.5  # Overall scale factor
    # Optional individual axis scaling
    scalex: 1.0
    scaley: 1.0
    scalez: 1.0
    # Offset from player position (in blocks)
    offset:
      x: -0.35
      y: -0.25
      z: 0.05
    # Rotation (in degrees)
    rotation:
      x: 0
      y: 0
      z: 45
    # Crouch-specific adjustments
    crouch:
      offset:
        x: 0
        y: -0.2
        z: 0.15
      rotation:
        x: 25
        y: 0
        z: 0
    # Optional: specify a different item to display
    # item: TRIDENT
    # For ItemsAdder items, use namespace:id format (e.g., itemsadder:ruby_trident)
    # item: itemsadder:ruby_trident

  - # Second entity (decorative element)
    scale: 0.3
    offset:
      x: -0.25
      y: -0.15
      z: 0.3
    rotation:
      x: 0
      y: 0
      z: 90
    # Crouch-specific adjustments
    crouch:
      offset:
        x: 0
        y: -0.2
        z: 0.1
    # Using a different item for this entity
    item: PRISMARINE_SHARD

  - # Third entity (decorative element)
    scale: 0.3
    offset:
      x: -0.10
      y: -0.04
      z: 0.3
    rotation:
      x: 0
      y: 180
    # Crouch-specific adjustments
    crouch:
      offset:
        y: -0.2
        z: 0.1
    item: PRISMARINE_SHARD
```

Each entity can have 8 parameters:
- scale: sets the scale of the entity
- scalex (optional): sets the scale in the x axis of the entity
- scaley (optional): sets the scale in the y axis of the entity
- scalez (optional): sets the scale in the z axis of the entity
- offset: set the offset in x/y/z
- rotation: set the rotation in x/y/z
- item (optional): set the item for this entity. By default, it will be the sheathable item the model is associated with.
- crouch (optional): set the offset (x/y/z) and rotation (x/y/z) for the crouch behavior
